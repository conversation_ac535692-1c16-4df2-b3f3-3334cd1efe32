import { configureStore } from '@reduxjs/toolkit';
import { combineReducers } from 'redux';
import authReducer from '../features/auth/authSlice';
import hospitalReducer from '../features/hospitals/hospitalSlice';
import registerReducer from '../features/register/registerSlice';

const rootReducer = combineReducers({
  auth: authReducer,
  hospitals: hospitalReducer,
  register: registerReducer,
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

