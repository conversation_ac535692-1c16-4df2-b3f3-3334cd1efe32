import { useState, useEffect } from 'react';
import { 
  startOfMonth, 
  endOfMonth, 
  startOfWeek, 
  endOfWeek, 
  format, 
  isSameMonth, 
  isSameDay, 
  addDays 
} from 'date-fns';
import { cn } from '@/lib/utils';

export function Calendar({
  value,
  onChange,
  className,
  showThaiYear = false,
  disabled = false,
  placeholder = "Select date",
  ...props
}) {
  const [selectedDate, setSelectedDate] = useState(value);
  const [currentMonth, setCurrentMonth] = useState(value ? new Date(value) : new Date());
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (value) {
      setSelectedDate(value);
      setCurrentMonth(new Date(value));
    }
  }, [value]);

  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const handleDateSelect = (date) => {
    setSelectedDate(date);
    onChange?.(date);
    setIsOpen(false);
  };

  const handleMonthChange = (monthIndex) => {
    const newDate = new Date(currentMonth.getFullYear(), monthIndex, 1);
    setCurrentMonth(newDate);
  };

  const handleYearChange = (year) => {
    const newDate = new Date(year, currentMonth.getMonth(), 1);
    setCurrentMonth(newDate);
  };

  const renderDays = () => {
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    return (
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day, index) => (
          <div
            key={day}
            className={cn(
              "text-center text-sm font-medium py-2",
              index === 0 || index === 6 ? "text-gray-400" : "text-gray-700"
            )}
          >
            {day}
          </div>
        ))}
      </div>
    );
  };

  const renderCells = () => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(monthStart);
    const startDate = startOfWeek(monthStart);
    const endDate = endOfWeek(monthEnd);

    const rows = [];
    let days = [];
    let day = startDate;

    while (day <= endDate) {
      for (let i = 0; i < 7; i++) {
        const isDisabled = !isSameMonth(day, monthStart);
        const isSelected = selectedDate && isSameDay(day, selectedDate);
        const currentDay = new Date(day);

        days.push(
          <button
            key={format(currentDay, 'yyyy-MM-dd')}
            type="button"
            onClick={() => !isDisabled && handleDateSelect(currentDay)}
            disabled={isDisabled}
            className={cn(
              "w-8 h-8 text-sm rounded-md transition-colors",
              "hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500",
              isSelected && "bg-blue-500 text-white hover:bg-blue-600",
              isDisabled && "text-gray-300 cursor-not-allowed hover:bg-transparent",
              !isSelected && !isDisabled && "text-gray-700"
            )}
          >
            {format(currentDay, 'd')}
          </button>
        );
        day = addDays(day, 1);
      }
      rows.push(
        <div key={day.toString()} className="grid grid-cols-7 gap-1">
          {days}
        </div>
      );
      days = [];
    }

    return rows;
  };

  const formatDisplayValue = () => {
    if (!selectedDate) return "";
    
    if (showThaiYear) {
      const thaiYear = selectedDate.getFullYear() + 543;
      return format(selectedDate, 'dd/MM/') + thaiYear;
    }
    
    return format(selectedDate, 'dd/MM/yyyy');
  };

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    
    for (let i = currentYear - 100; i <= currentYear + 10; i++) {
      years.push(i);
    }
    
    return years;
  };

  return (
    <div className={cn("relative", className)} {...props}>
      <input
        type="text"
        value={formatDisplayValue()}
        placeholder={placeholder}
        readOnly
        disabled={disabled}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={cn(
          "w-full bg-[#F0EFEB] border-0 rounded-md px-3 py-2 cursor-pointer",
          "focus:outline-none focus:ring-2 focus:ring-blue-500",
          disabled && "cursor-not-allowed opacity-50"
        )}
      />
      
      {isOpen && !disabled && (
        <div className="absolute top-full left-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg p-4 min-w-[300px]">
          {/* Month and Year Selectors */}
          <div className="flex justify-between items-center mb-4">
            <select
              value={currentMonth.getMonth()}
              onChange={(e) => handleMonthChange(parseInt(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              {months.map((month, index) => (
                <option key={index} value={index}>
                  {month}
                </option>
              ))}
            </select>
            
            <select
              value={currentMonth.getFullYear()}
              onChange={(e) => handleYearChange(parseInt(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              {generateYearOptions().map((year) => (
                <option key={year} value={year}>
                  {showThaiYear ? year + 543 : year}
                </option>
              ))}
            </select>
          </div>
          
          {/* Calendar Grid */}
          <div>
            {renderDays()}
            {renderCells()}
          </div>
          
          {/* Close button */}
          <div className="mt-4 text-center">
            <button
              type="button"
              onClick={() => setIsOpen(false)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      )}
      
      {/* Backdrop to close calendar */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
