import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import Header from './Header';
import Footer from './Footer';
import LoginForm from './Login';
import RegisterForm from './RegisterForm';

const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true);
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <Header isLogin={isLogin} setIsLogin={setIsLogin} />

      {/* Main Content */}
      <main className={"flex-1 flex  justify-center p-4 md:p-6" + (isLogin ? " items-center" : " ")} style={{ backgroundColor: '#e8e8e8' }}>
        <div className="w-full max-w-2xl -mt-15">
          <Card className="shadow-lg bg-white">
            <CardContent>
              {isLogin ? (
                <LoginForm />
              ) : (
                <div className="w-full flex justify-center">
                  <div className="w-full max-w-xl"> {/* Adjust max-w-xl / lg / 2xl as needed */}
                    <RegisterForm onCancel={() => setIsLogin(true)} />
                  </div>
                </div>

              )}
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default AuthPage;