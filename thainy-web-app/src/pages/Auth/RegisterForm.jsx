import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useDispatch, useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { FileText, Image, Trash2 } from 'lucide-react';
import FolderIcon from '@/assets/img/Folder.svg';
import { registerDefaultValues, registerSchema } from '../../schemas/register/registerSchema';
import { fetchHospitalsThunk } from '../../features/hospitals/hospitalSlice';
import { registerUserThunk, resetRegisterState } from '../../features/register/registerSlice';
import { Calendar } from '@/components/ui/calendar';
import { toast } from "react-toastify";
import { useLanguage } from '@/contexts/LanguageContext';
import { convertBEToUTC } from '../../utils/dateUtils';


export default function RegisterForm({ onCancel }) {
    const dispatch = useDispatch();
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const { t, currentLanguage } = useLanguage();

    // Get state from Redux
    const { hospitals, loading: hospitalsLoading } = useSelector(state => state.hospitals);
    const { loading: registerLoading, error: registerError, success } = useSelector(state => state.register);

    const registerForm = useForm({
        resolver: zodResolver(registerSchema),
        defaultValues: registerDefaultValues
    });

    // Fetch hospitals when component mounts
    useEffect(() => {
        dispatch(fetchHospitalsThunk());
    }, []);

    const handleFileUpload = (event) => {
        const files = Array.from(event.target.files);
        const newFiles = files.map(file => ({
            id: Date.now() + Math.random(),
            name: file.name,
            type: file.type,
            file: file
        }));
        setUploadedFiles(prev => [...prev, ...newFiles]);
    };

    const removeFile = (fileId) => {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    };

    const onRegisterSubmit = async (data) => {
        // console
        try {
            if (data.dateOfBirth) {
                data.dateOfBirth = convertBEToUTC(data.dateOfBirth);
            }
            await dispatch(registerUserThunk(data)).unwrap();
            toast.success("Registration successful!");
        } catch (error) {
            toast.error(`Registration failed. ${error.message}`);
        }
    };

    return (
        <Form {...registerForm}>
            <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)}>
                {/* Hospital Selection */}

                <FormField
                    control={registerForm.control}
                    name="hospital"
                    render={({ field }) => (
                        <FormItem className="mb-3">
                            <FormLabel className="text-sm font-medium !text-gray-700">Hospital</FormLabel>
                            <div className="w-full overflow-hidden">
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger className="!w-full max-w-full bg-[#F0EFEB] border-0 overflow-hidden">
                                            <SelectValue
                                                placeholder="Select hospital"
                                                className="truncate"
                                            />
                                        </SelectTrigger>
                                    </FormControl>
                                <SelectContent className="w-full max-h-[200px] overflow-y-auto">
                                    <div className="px-2 py-2 border-b sticky top-0 bg-white">
                                        <input
                                            type="text"
                                            placeholder="Search hospitals..."
                                            className="w-full px-2 py-1.5 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400"
                                            onChange={(e) => {
                                                const searchTerm = e.target.value.toLowerCase();
                                                const items = document.querySelectorAll('[data-hospital-item]');
                                                items.forEach(item => {
                                                    const text = item.textContent.toLowerCase();
                                                    item.style.display = text.includes(searchTerm) ? 'flex' : 'none';
                                                });
                                            }}
                                        />
                                    </div>
                                    {hospitalsLoading ? (
                                        <SelectItem value="loading" disabled>Loading hospitals...</SelectItem>
                                    ) : hospitals.length > 0 ? (
                                        hospitals.map(hospital => (
                                            <SelectItem
                                                key={hospital.hospital_number}
                                                value={hospital.hospital_name || "N/A"}
                                                data-hospital-item
                                                className="cursor-pointer hover:bg-gray-50 px-2 py-2"
                                            >
                                                <span className="truncate">
                                                    {hospital.hospital_name
                                                        ? `${hospital.hospital_name} (${hospital.hospital_number})`
                                                        : `Hospital ${hospital.hospital_number}`}
                                                </span>
                                            </SelectItem>
                                        ))
                                    ) : (
                                        <SelectItem value="no-hospitals" disabled>No hospitals available</SelectItem>
                                    )}
                                </SelectContent>

                            </Select>
                            </div>
                            <FormMessage className="text-xs text-red-500 mt-1" />
                        </FormItem>
                    )}
                />





                {/* Role Selection */}
                <FormField
                    control={registerForm.control}
                    name="role"
                    render={({ field }) => (
                        <FormItem className="mb-3 gap-0">
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Role")}</FormLabel>
                            <FormControl>
                                <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="flex flex-row space-x-6 flex-wrap"
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="Leader" id="leader" className="border-[#7FC5C6]" />
                                        <Label htmlFor="leader" className="text-sm">{t.t("Leader")}</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="Doctor" id="doctor" className="border-[#7FC5C6]" />
                                        <Label htmlFor="doctor" className="text-sm">{t.t("Doctor")}</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="Collector" id="collector" className="border-[#7FC5C6]" />
                                        <Label htmlFor="collector" className="text-sm">{t.t("Collector")}</Label>
                                    </div>
                                </RadioGroup>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Name Fields */}
                <div className="mb-3">
                    <Label className="text-sm font-medium text-gray-700 block">{t.t("First name - Last name")}</Label>
                    <div className="flex flex-col sm:flex-row gap-4">
                        <FormField
                            control={registerForm.control}
                            name="firstName"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormControl>
                                        <Input
                                            placeholder={t.t("Please enter your first name")}
                                            className="w-full bg-[#F0EFEB] border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={registerForm.control}
                            name="lastName"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormControl>
                                        <Input
                                            placeholder={t.t("Please enter your last name")}
                                            className="w-full bg-[#F0EFEB] border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                {/* Date of Birth and Telephone */}
                <div className="flex flex-col sm:flex-row gap-4 mb-3">
                    <FormField
                        control={registerForm.control}
                        name="dateOfBirth"
                        render={({ field }) => (
                            <FormItem className="flex-1 gap-0">
                                <FormLabel className="text-sm font-medium !text-gray-700">
                                    {t.t("Date of Birth")}
                                    {currentLanguage === "ไทย" ? " (B.E)" : currentLanguage === "English" ? " (B.E)" : ""}
                                </FormLabel>
                                <FormControl>
                                    <Calendar
                                        value={
                                            field.value
                                                ? (() => {
                                                    // Convert BE → AD for Calendar
                                                    const [y, m, d] = field.value.split("-");
                                                    const adYear = parseInt(y, 10) - 543;
                                                    return new Date(adYear, m - 1, d);
                                                })()
                                                : null
                                        }
                                        onChange={(date) => {
                                            if (date) {
                                                const thaiYear = date.getFullYear() + 543;
                                                const thaiDate = `${thaiYear}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(
                                                    date.getDate()
                                                ).padStart(2, "0")}`;
                                                field.onChange(thaiDate);
                                            } else {
                                                field.onChange("");
                                            }
                                        }}
                                        showThaiYear={true}
                                        placeholder={t.t("Select date")}
                                        className="w-full"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={registerForm.control}
                        name="telephone"
                        render={({ field }) => (
                            <FormItem className="flex-1 gap-0">
                                <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Telephone")}</FormLabel>
                                <FormControl>
                                    <Input
                                        type="tel"
                                        placeholder={t.t("Please enter your Telephone")}
                                        className="w-full bg-[#F0EFEB] border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Email */}
                <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem className="mb-3 gap-0">
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Email")}</FormLabel>
                            <FormControl>
                                <Input
                                    type="email"
                                    placeholder={t.t("Please enter your email")}
                                    className="w-full bg-[#F0EFEB] border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Username */}
                <FormField
                    control={registerForm.control}
                    name="username"
                    render={({ field }) => (
                        <FormItem className="mb-3 gap-0">
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Username")}</FormLabel>
                            <FormControl>
                                <Input
                                    placeholder={t.t("Please enter your username")}
                                    className="w-full bg-[#F0EFEB] border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Password Fields */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <FormField
                        control={registerForm.control}
                        name="password"
                        render={({ field }) => (
                            <FormItem className="flex-1 gap-0">
                                <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Password")}</FormLabel>
                                <FormControl>
                                    <Input
                                        type="password"
                                        placeholder={t.t("Please enter your password")}
                                        className="w-full bg-[#F0EFEB] border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={registerForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                            <FormItem className="flex-1 gap-0">
                                <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Confirm Password")}</FormLabel>
                                <FormControl>
                                    <Input
                                        type="password"
                                        placeholder={t.t("Please re-enter your password")}
                                        className="w-full bg-[#F0EFEB] border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Password Requirements */}
                <div className="text-xs text-red-500 mb-3">
                    <p>{t.t("The password must contain at least 1 uppercase letter, 1")}</p>
                    <p>{t.t("lowercase letter, and 1 number, with a length of 6-12")}</p>
                    <p>{t.t("characters")}.</p>
                </div>

                {/* Certificate Attachments */}
                <div className="rounded-lg border border-gray-200 shadow-sm bg-[#F0EFEB] ">
                    {/* Header Row */}
                    <div className="flex items-center justify-between  rounded-md bg-white shadow-md p-3">
                        <div>
                            <h3 className="text-sm font-semibold text-gray-800">{t.t("Certificate Attachments")}</h3>
                            <p className="text-xs text-gray-500">
                                {t.t("Number of files uploaded:")}{" "}
                                <span className="font-medium">{uploadedFiles.length} {t.t("files")}</span>
                            </p>
                        </div>

                        {/* File Upload Button */}
                        <label htmlFor="file-upload" className="cursor-pointer">
                            <div className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-md shadow hover:bg-gray-800 transition">
                                <img src={FolderIcon} alt="Folder" className="w-4 h-4" />
                                <span className="text-sm font-medium">{t.t("Select file")}</span>
                            </div>
                            <input
                                id="file-upload"
                                type="file"
                                multiple
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={handleFileUpload}
                                className="hidden"
                            />
                        </label>
                    </div>


                    <div className=" p-3 ">
                        {/* Uploaded Files List */}
                        {uploadedFiles.length > 0 && (
                            <div className=" ">
                                {uploadedFiles.map((file) => (
                                    <div
                                        key={file.id}
                                        className="flex items-center justify-between px-4 py-2 bg-gray-50 border border-gray-200" // removed rounded-md here
                                    >
                                        <div className="flex items-center gap-2">
                                            {file.type.includes("pdf") ? (
                                                <FileText className="w-4 h-4 text-red-500" />
                                            ) : (
                                                <Image className="w-4 h-4 text-blue-500" />
                                            )}
                                            <span className="text-sm text-gray-700 font-medium">{file.name}</span>
                                        </div>

                                        <button
                                            type="button"
                                            onClick={() => removeFile(file.id)}
                                            className="flex items-center gap-1 text-xs font-medium text-red-500 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                            {t.t("delete")}
                                        </button>
                                    </div>
                                ))}
                            </div>

                        )}
                    </div>
                </div>



                {/* Display error message if registration fails */}
                {registerError && (
                    <div className="text-red-500 text-sm text-center mb-4">
                        {registerError}
                    </div>
                )}

                {/* Display success message */}
                {success && (
                    <div className="text-green-500 text-sm text-center mb-4">
                        Registration successful! Please check your email for verification.
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row justify-center gap-5 mt-3">
                    <Button
                        type="button"
                        variant="outline"
                        className="flex px-10 py-4 text-white"
                        style={{ backgroundColor: '#5fb3b3' }}
                        onClick={onCancel}
                        disabled={registerLoading}
                    >
                        {t.t("Cancel")}
                    </Button>
                    <Button
                        type="submit"
                        className="flex px-10 py-4"
                        disabled={registerLoading}
                        style={{ backgroundColor: '#5fb3b3' }}
                    >
                        {registerLoading ? t.t('Registering...') : t.t('Register')}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
