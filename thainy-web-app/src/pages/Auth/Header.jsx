import { useLanguage } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';
import MainLogo from '@/assets/img/Main_logo.svg';

export default function Header({ isLogin, setIsLogin }) {
    const { t, locale, updateLanguage } = useLanguage();

    const toggleLanguage = () => {
        const newLanguage = locale === 'en' ? 'th' : 'en';
        updateLanguage(newLanguage);
    };

    return (
        <header className="flex w-full justify-center p-4 md:p-6 h-[196px]" style={{ backgroundColor: '#50A0A0' }}>
            {/* Centered max-width container */}
            <div className="flex w-full md:max-w-4xl items-center justify-start p-4 md:p-6">
                <div className="flex flex-col space-y-2">
                    {/* Row 1: Logo + Title */}
                    <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                            <img src={MainLogo} alt="ThaiNy Logo" className="w-28 h-28" />
                        </div>
                        <h1 className="text-xl md:text-2xl font-bold text-white">
                            {t.t("ThaiNy Training System")}
                        </h1>
                    </div>

                    {/* Row 2: Login + Register */}
                    <div className="flex flex-wrap gap-4 pl-14">
                        <button
                            onClick={() => setIsLogin(true)}
                            className={`text-sm md:text-base ${isLogin ? "text-white font-semibold" : "text-white/80"
                                }`}
                        >
                            {t.t("Login")}
                        </button>
                        <button
                            onClick={() => setIsLogin(false)}
                            className={`text-sm md:text-base ${!isLogin
                                    ? "text-[#FFD580] font-semibold border-b-2 border-[#FFD580]"
                                    : "text-white/80"
                                }`}
                        >
                            {t.t("Register")}
                        </button>

                    </div>
                </div>
            </div>
            {/* Language Toggle Button */}
            <div className="flex items-center">
                <button
                    onClick={toggleLanguage}
                    className="flex items-center gap-2 px-3 py-1 text-white border border-white/30 rounded-md hover:bg-white/10 transition-colors text-sm font-medium"
                >
                    <Globe className="w-4 h-4" />
                    {locale === 'en' ? 'ไทย' : 'EN'}
                </button>
            </div>

        </header>
    );
}
