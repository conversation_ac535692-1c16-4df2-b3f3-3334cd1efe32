import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { loginUser } from '../../features/auth/authSlice';
import { useLanguage } from '@/contexts/LanguageContext';

// Validation schema
const loginSchema = z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
});

export default function LoginForm() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { loading, error } = useSelector((state) => state.auth);
    const { t } = useLanguage();

    const loginForm = useForm({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
        },
    });

    const onLoginSubmit = async (data) => {
        try {
            await dispatch(loginUser(data)).unwrap();
            navigate('/dashboard');
        } catch (error) {
            console.error('Login failed:', error);
        }
    };
console.log("error",error)
    return (
        <Form {...loginForm}>
            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">{t.t("Email")}</FormLabel>
                            <FormControl>
                                <Input
                                    type="email"
                                    placeholder={t.t("Enter your email")}
                                    className="w-full bg-gray-100 border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium text-gray-700">{t.t("Password")}</FormLabel>
                            <FormControl>
                                <Input
                                    type="password"
                                    placeholder={t.t("Enter your password")}
                                    className="w-full bg-gray-100 border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                {/* Display error message if login fails */}
                {error && (
                    <div className="text-red-500 text-sm text-center mb-4">
                        {error}
                    </div>
                )}

                <Button
                    type="submit"
                    className="w-full"
                    size="lg"
                    disabled={loading}
                    style={{ backgroundColor: '#5fb3b3' }}
                >
                    {loading ? t.t('Signing In...') : t.t('Sign In')}
                </Button>
            </form>
        </Form>
    );
}